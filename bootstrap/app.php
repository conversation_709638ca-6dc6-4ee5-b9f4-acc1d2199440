<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
        ]);

        // Add the eager loading middleware to the web group
        $middleware->web(append: [
            \App\Http\Middleware\EagerLoadUserProfile::class,
            // CSP is now handled by Nginx configuration
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
